{"version": 4, "terraform_version": "1.5.7", "serial": 4, "lineage": "557a652e-5993-af02-2cdb-81f4217169b1", "outputs": {}, "resources": [{"mode": "data", "type": "digitalocean_kubernetes_cluster", "name": "cluster", "provider": "provider[\"registry.terraform.io/digitalocean/digitalocean\"]", "instances": [{"schema_version": 0, "attributes": {"amd_gpu_device_metrics_exporter_plugin": [{"enabled": false}], "amd_gpu_device_plugin": [{"enabled": false}], "auto_upgrade": false, "cluster_autoscaler_configuration": [], "cluster_subnet": "**********/16", "control_plane_firewall": [], "created_at": "2025-08-16 14:50:42 +0000 UTC", "endpoint": "https://8e8be64e-f826-4b40-9f20-eefaef42fce8.k8s.ondigitalocean.com", "ha": false, "id": "8e8be64e-f826-4b40-9f20-eefaef42fce8", "ipv4_address": "", "kube_config": [{"client_certificate": "", "client_key": "", "cluster_ca_certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURKekNDQWcrZ0F3SUJBZ0lDQm5Vd0RRWUpLb1pJaHZjTkFRRUxCUUF3TXpFVk1CTUdBMVVFQ2hNTVJHbG4KYVhSaGJFOWpaV0Z1TVJvd0dBWURWUVFERXhGck9ITmhZWE1nUTJ4MWMzUmxjaUJEUVRBZUZ3MHlOVEE0TVRZeApORFV3TkRsYUZ3MDBOVEE0TVRZeE5EVXdORGxhTURNeEZUQVRCZ05WQkFvVERFUnBaMmwwWVd4UFkyVmhiakVhCk1CZ0dBMVVFQXhNUmF6aHpZV0Z6SUVOc2RYTjBaWElnUTBFd2dnRWlNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0SUIKRHdBd2dnRUtBb0lCQVFERzdiUWd0alo3cHUrakJIcTQvdkJiQTVjaVVZY2dUZmlBT3hkTndoZzEzZGVPSnJORwp4Uk5VVVpzdVZoeVBMVkJLb2gyYmZ1RkJZUFQ0cHFxRXRXSitKSE95K3hlVTRrUmJYQ2NuRXdobWVjTEowdFRnCmx4aE9xd2szR054SjBZZVBvK0Y4dzAyLytFaFgyZVUzN3VwWFlHcU4weTN3N0k3RzhKUSs5YU9xUjhxd3o1T08KcTlydHZ3OU5PcndMaDM0SHNWQU5ab1F4VXNBdkY4V1lsam9JZHF0R0tQQ2hkMnZaMmRzWFZXLzk1ZDg5Q1JsNwp5YjlUMUdGZDJwMlBDUXhubURESytEbkdnV2toNW1DODhoU3NDSGtnRUV3ZXZDaUc3K2lpQXRlTGhHS2tqYWRUCkNKZGIxZWNLZWltdEtjeGNqNGxHdWI5RzZxdk0zd3RZM1p6OUFnTUJBQUdqUlRCRE1BNEdBMVVkRHdFQi93UUUKQXdJQmhqQVNCZ05WSFJNQkFmOEVDREFHQVFIL0FnRUFNQjBHQTFVZERnUVdCQlNtUE9UVFRsWTEvM0dYWm1RaQorSE8wZ0RVSzdUQU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFBR3liS29WNjJhTDlaZlgrWHd1OEhDS1hFM1diClB5R0tQMG9CWnZZWmRDZ016WUlNYllFTWdDdnRrWnNQSkJ0ZG54Ti9qNU8rRGd5Q254dzMwdnBnZ3h2emVaQmUKTjhUT1BMVldieFFaTThmRUVwT0FUTzhLTkIvTklmT00wZXhHeStiNmV0UFdHRGhtQWF6TVZ1cVkyNHJ6YWp2LwptRDBNampCQ2g2aUI2THZrOUNaL1kyellNelBuamVQa2VRb3NJdjViemFuR25CZ2gxdHIycGRYUzNvc3NNSnZoCmxmNklwb3cyaGhRd3MwdGtUNzdnWjhMNWRoaGFaaFUyaUtlODBZSHlWWmYvaWx4QmE1S3pmZG5VMFlIQ3lZbUsKRUJBUGVvVWNKdEc1QThNVm9NbzEyVjg5emRvWlZ6MmxyRUlaQmdOK2lJNm1GK2srVGNKMTA0Ykt3QT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K", "expires_at": "2025-08-29T08:09:35Z", "host": "https://8e8be64e-f826-4b40-9f20-eefaef42fce8.k8s.ondigitalocean.com", "raw_config": "apiVersion: v1\nkind: Config\nclusters:\n- cluster:\n    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURKekNDQWcrZ0F3SUJBZ0lDQm5Vd0RRWUpLb1pJaHZjTkFRRUxCUUF3TXpFVk1CTUdBMVVFQ2hNTVJHbG4KYVhSaGJFOWpaV0Z1TVJvd0dBWURWUVFERXhGck9ITmhZWE1nUTJ4MWMzUmxjaUJEUVRBZUZ3MHlOVEE0TVRZeApORFV3TkRsYUZ3MDBOVEE0TVRZeE5EVXdORGxhTURNeEZUQVRCZ05WQkFvVERFUnBaMmwwWVd4UFkyVmhiakVhCk1CZ0dBMVVFQXhNUmF6aHpZV0Z6SUVOc2RYTjBaWElnUTBFd2dnRWlNQTBHQ1NxR1NJYjNEUUVCQVFVQUE0SUIKRHdBd2dnRUtBb0lCQVFERzdiUWd0alo3cHUrakJIcTQvdkJiQTVjaVVZY2dUZmlBT3hkTndoZzEzZGVPSnJORwp4Uk5VVVpzdVZoeVBMVkJLb2gyYmZ1RkJZUFQ0cHFxRXRXSitKSE95K3hlVTRrUmJYQ2NuRXdobWVjTEowdFRnCmx4aE9xd2szR054SjBZZVBvK0Y4dzAyLytFaFgyZVUzN3VwWFlHcU4weTN3N0k3RzhKUSs5YU9xUjhxd3o1T08KcTlydHZ3OU5PcndMaDM0SHNWQU5ab1F4VXNBdkY4V1lsam9JZHF0R0tQQ2hkMnZaMmRzWFZXLzk1ZDg5Q1JsNwp5YjlUMUdGZDJwMlBDUXhubURESytEbkdnV2toNW1DODhoU3NDSGtnRUV3ZXZDaUc3K2lpQXRlTGhHS2tqYWRUCkNKZGIxZWNLZWltdEtjeGNqNGxHdWI5RzZxdk0zd3RZM1p6OUFnTUJBQUdqUlRCRE1BNEdBMVVkRHdFQi93UUUKQXdJQmhqQVNCZ05WSFJNQkFmOEVDREFHQVFIL0FnRUFNQjBHQTFVZERnUVdCQlNtUE9UVFRsWTEvM0dYWm1RaQorSE8wZ0RVSzdUQU5CZ2txaGtpRzl3MEJBUXNGQUFPQ0FRRUFBR3liS29WNjJhTDlaZlgrWHd1OEhDS1hFM1diClB5R0tQMG9CWnZZWmRDZ016WUlNYllFTWdDdnRrWnNQSkJ0ZG54Ti9qNU8rRGd5Q254dzMwdnBnZ3h2emVaQmUKTjhUT1BMVldieFFaTThmRUVwT0FUTzhLTkIvTklmT00wZXhHeStiNmV0UFdHRGhtQWF6TVZ1cVkyNHJ6YWp2LwptRDBNampCQ2g2aUI2THZrOUNaL1kyellNelBuamVQa2VRb3NJdjViemFuR25CZ2gxdHIycGRYUzNvc3NNSnZoCmxmNklwb3cyaGhRd3MwdGtUNzdnWjhMNWRoaGFaaFUyaUtlODBZSHlWWmYvaWx4QmE1S3pmZG5VMFlIQ3lZbUsKRUJBUGVvVWNKdEc1QThNVm9NbzEyVjg5emRvWlZ6MmxyRUlaQmdOK2lJNm1GK2srVGNKMTA0Ykt3QT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K\n    server: https://8e8be64e-f826-4b40-9f20-eefaef42fce8.k8s.ondigitalocean.com\n  name: do-sgp1-k8s-ops\ncontexts:\n- context:\n    cluster: do-sgp1-k8s-ops\n    user: do-sgp1-k8s-ops-admin\n  name: do-sgp1-k8s-ops\ncurrent-context: do-sgp1-k8s-ops\nusers:\n- name: do-sgp1-k8s-ops-admin\n  user:\n    token: ***********************************************************************\n", "token": "***********************************************************************"}], "kubeconfig_expire_seconds": null, "maintenance_policy": [{"day": "any", "duration": "4h0m0s", "start_time": "4:00"}], "name": "k8s-ops", "node_pool": null, "region": "sgp1", "routing_agent": [{"enabled": false}], "service_subnet": "***********/19", "status": "running", "surge_upgrade": true, "tags": [], "updated_at": "2025-08-19 04:01:58 +0000 UTC", "urn": "do:kubernetes:8e8be64e-f826-4b40-9f20-eefaef42fce8", "version": "1.33.1-do.3", "vpc_uuid": "661724dc-d1ab-4406-b4d2-127dfd9b25b6"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "kubernetes_deployment", "name": "deployment", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 1, "attributes": {"id": "sample/sample-app", "metadata": [{"annotations": {}, "generate_name": "", "generation": 2, "labels": {}, "name": "sample-app", "namespace": "sample", "resource_version": "1686417", "uid": "c5e57d9e-d5c4-42bc-9daf-9c51dc8977e1"}], "spec": [{"min_ready_seconds": 0, "paused": false, "progress_deadline_seconds": 600, "replicas": "1", "revision_history_limit": 10, "selector": [{"match_expressions": [], "match_labels": {"app": "sample-app"}}], "strategy": [{"rolling_update": [{"max_surge": "25%", "max_unavailable": "25%"}], "type": "RollingUpdate"}], "template": [{"metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {"app": "sample-app"}, "name": "", "namespace": "", "resource_version": "", "uid": ""}], "spec": [{"active_deadline_seconds": 0, "affinity": [], "automount_service_account_token": true, "container": [{"args": [], "command": [], "env": [{"name": "ENV", "value": "production", "value_from": []}], "env_from": [], "image": "nginx:1.21", "image_pull_policy": "Always", "lifecycle": [], "liveness_probe": [], "name": "sample-app", "port": [{"container_port": 80, "host_ip": "", "host_port": 0, "name": "http", "protocol": "TCP"}], "readiness_probe": [], "resources": [{"limits": {}, "requests": {}}], "security_context": [], "startup_probe": [], "stdin": false, "stdin_once": false, "termination_message_path": "/dev/termination-log", "termination_message_policy": "File", "tty": false, "volume_device": [], "volume_mount": [], "working_dir": ""}], "dns_config": [], "dns_policy": "ClusterFirst", "enable_service_links": true, "host_aliases": [], "host_ipc": false, "host_network": false, "host_pid": false, "hostname": "", "image_pull_secrets": [], "init_container": [], "node_name": "", "node_selector": {}, "os": [], "priority_class_name": "", "readiness_gate": [], "restart_policy": "Always", "runtime_class_name": "", "scheduler_name": "default-scheduler", "security_context": [], "service_account_name": "", "share_process_namespace": false, "subdomain": "", "termination_grace_period_seconds": 30, "toleration": [], "topology_spread_constraint": [], "volume": []}]}]}], "timeouts": null, "wait_for_rollout": true}, "sensitive_attributes": [], "private": "****************************************************************************************************************************************************************************", "dependencies": ["data.digitalocean_kubernetes_cluster.cluster"]}]}], "check_results": null}